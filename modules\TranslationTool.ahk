; ============================================================================
; 翻译工具模块 (TranslationTool.ahk)
; 功能: 选中文本自动翻译
; 说明: 使用Alt+`快捷键获取选中文本并启动翻译工具
; ============================================================================

; Alt+`: 获取选中文本并启动翻译工具
!`::
{
    ; 保存当前剪贴板内容
    clipSaved := ClipboardAll()
    
    ; 清空剪贴板
    A_Clipboard := ""
    
    ; 发送 Ctrl+C 复制选中文本
    Send "^c"
    
    ; 等待剪贴板有新内容
    if ClipWait(0.1) {
        ; 获取选中的文本
        selectedText := A_Clipboard
        ; 可选：显示选中的文本（调试用）
        ; MsgBox selectedText,, "T3"
        
        ; 运行 WindowCC 翻译程序
        Run "WindowCC.exe"
    } else {
        MsgBox "未获取到选中的文本！",, "T3"
    }
    
    ; 延迟1秒再恢复剪贴板内容
    Sleep 1000
    
    ; 恢复原来的剪贴板内容
    A_Clipboard := clipSaved
}

; ============================================================================
; 已注释的微信发送功能（保留备用）
; ============================================================================
; 发送ctrl+c复制选中文本然后运行exe发送剪切板内容到微信群
; F4::
; {
;    clipSaved := ClipboardAll  ; 保存当前剪贴板内容
;     Send("^c")  ; 发送 Ctrl + C 复制选中文本
;     ClipWait(1)  ; 等待文本复制到剪贴板
;     Run("C:\\path\\wxbot\\crtlvsend.exe C:\\path\\wxbot\\config.json")  ; 运行记事本
;     Clipboard := clipSaved  ; 恢复剪贴板原有内容
;     clipSaved := ""  ; 清除剪贴板变量
;     Return
; }
