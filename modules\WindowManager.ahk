; ============================================================================
; 窗口管理模块 (WindowManager.ahk)
; 功能: 提供窗口操作的通用函数
; 说明: 包含窗口激活、移动、最大化等功能的通用函数
; ============================================================================

; 等待Windows Terminal窗口出现的函数
WaitForTerminalWindow() {
    windowFound := false
    
    ; 尝试多种窗口识别方式，最多等待10秒
    Loop 10 {
        Sleep(1000)
        
        ; 尝试按进程名查找
        if WinExist("ahk_exe WindowsTerminal.exe") {
            windowFound := true
            break
        }
        
        ; 也可以尝试按窗口类名查找
        if WinExist("ahk_class CASCADIA_HOSTING_WINDOW_CLASS") {
            windowFound := true
            break
        }
    }
    
    return windowFound
}

; ============================================================================
; 辅助函数
; ============================================================================

; 获取资源管理器当前路径的函数
GetExplorerPath() {
    hwnd := WinExist("A")
    for window in ComObject("Shell.Application").Windows {
        if (window.hwnd = hwnd) {
            try {
                url := window.LocationURL
                ; 解码 URL 编码的路径
                path := UrlDecode(url)
                ; 移除 "file:///" 前缀
                path := RegExReplace(path, "^file:///", "")
                ; 将正斜杠替换为反斜杠
                path := StrReplace(path, "/", "\\")
                return path
            }
        }
    }
    return ""
}

; URL 解码函数
UrlDecode(str) {
    Loop {
        if !RegExMatch(str, "%([0-9A-Fa-f]{2})", &match) {
            break
        }
        char := Chr("0x" . match[1])
        str := StrReplace(str, match[0], char)
    }
    return str
}


; Ctrl+N: 记事本窗口管理示例
^n::
{
    if WinExist("ahk_class Notepad")
        WinActivate  ; 激活已存在的记事本窗口
    else
        Run "notepad"  ; 打开新的记事本窗口
}
