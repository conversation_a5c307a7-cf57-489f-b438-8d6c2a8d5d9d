; AutoHotkey v2.0.19主配置文件

; 引入各个功能模块
; 键盘映射模块 - 自定义按键映射(右Alt→Home, 右Ctrl→End)
#Include "modules\KeyMapping.ahk"

; 文本替换模块 - 快速文本替换和代码片段插入
#Include "modules\TextReplacement.ahk"

; 网站和工具启动模块 - 快捷键启动常用网站和工具
#Include "modules\WebsiteTools.ahk"

; 快捷键ALT+R - 快速打开资源管理器目录下的WindowsTerminal终端功能
#Include "modules\WindowsTerminal.ahk"

; 快捷键ALT+K - Windows Terminal启动Kimi并移动到第二显示器
#Include "modules\Kimi.ahk"

; 快捷键Alt+C - 使用WindowsTerminal快速打开资源管理器目录下的Crush终端功能
#Include "modules\Crush.ahk"

; 快捷键ALT+~ - 翻译工具模块 - 选中文本自动翻译功能
#Include "modules\TranslationTool.ahk"

; 快捷键CTRL+SHIFT+I - 多显示器信息模块 - 显示器配置信息查看
#Include "modules\MonitorInfo.ahk"
; 窗口管理模块 - 窗口操作通用函数库
#Include "modules\WindowManager.ahk"

; 脚本启动消息（可选）
; ToolTip "AutoHotkey脚本已加载完成！", 0, 0
; SetTimer () => ToolTip(), -2000  ; 2秒后隐藏提示
