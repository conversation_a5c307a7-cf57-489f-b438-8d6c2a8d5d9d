# AutoHotkey 脚本模块化重构

本项目将原始的 `我的脚本.ahk` 文件按照功能模块进行了重构，提高了代码的可读性和维护性。

## 文件结构

```
├── main.ahk                    # 主配置文件，引入所有模块
├── 我的脚本.ahk                 # 原始脚本文件（保留备份）
├── modules/                    # 功能模块目录
│   ├── KeyMapping.ahk          # 键盘映射模块
│   ├── TextReplacement.ahk     # 文本替换模块
│   ├── WebsiteTools.ahk        # 网站和工具启动模块
│   ├── TerminalManager.ahk     # 终端管理模块
│   ├── ExplorerIntegration.ahk # 资源管理器集成模块
│   ├── TranslationTool.ahk     # 翻译工具模块
│   ├── MonitorInfo.ahk         # 多显示器信息模块
│   └── WindowManager.ahk       # 窗口管理模块
└── README.md                   # 本说明文档
```

## 模块功能说明

### 1. KeyMapping.ahk - 键盘映射模块
- **功能**: 自定义键盘按键映射
- **快捷键**:
  - `右Alt` → `Home`键
  - `右Ctrl` → `End`键

### 2. TextReplacement.ahk - 文本替换模块
- **功能**: 快速文本替换和代码片段插入
- **快捷输入**:
  - `gobf` → Go编译命令（普通版本）
  - `gobw` → Go编译命令（Windows GUI版本）
  - `selectf` → SQL查询语句模板
  - `sedf` → Sed命令模板
  - `mainf` → Go语言main函数模板
  - `baiduf` → 打开百度网站

### 3. WebsiteTools.ahk - 网站和工具启动模块
- **功能**: 快捷键启动常用网站和工具
- **快捷键**:
  - `Alt+1` → 打开本地assetTerm网站
  - `Alt+2` → 打开聚合搜索网站
  - `Alt+3` → 打开堡垒机网站
  - `Ctrl+Alt+G` → 打开GitHub
  - `Ctrl+Alt+O` → 打开Google
  - `Ctrl+Alt+B` → 打开百度
  - `Ctrl+Alt+D` → 打开豆包
  - `Alt+G` → 启动谷歌验证工具

### 4. TerminalManager.ahk - 终端管理模块
- **功能**: 管理Windows Terminal的启动和窗口操作
- **快捷键**:
  - `Alt+K` → 启动Kimi逆向终端对话（自动移动到第二显示器并最大化）

### 5. ExplorerIntegration.ahk - 资源管理器集成模块
- **功能**: 在资源管理器中集成终端功能
- **快捷键**:
  - `Alt+R` → 在当前目录启动Crush终端对话（仅在资源管理器中有效）

### 6. TranslationTool.ahk - 翻译工具模块
- **功能**: 选中文本自动翻译
- **快捷键**:
  - `Alt+\`` → 获取选中文本并启动翻译工具

### 7. MonitorInfo.ahk - 多显示器信息模块
- **功能**: 显示当前显示器配置信息
- **快捷键**:
  - `Ctrl+Shift+I` → 显示显示器信息（用于调试）

### 8. WindowManager.ahk - 窗口管理模块
- **功能**: 提供窗口操作的通用函数
- **快捷键**:
  - `Ctrl+N` → 记事本窗口管理
- **函数**:
  - `WaitForTerminalWindow()` → 等待Terminal窗口出现

## 使用方法

1. **运行主脚本**: 双击 `main.ahk` 文件启动所有功能
2. **单独运行模块**: 可以单独运行某个模块文件进行测试
3. **自定义配置**: 根据需要修改各个模块文件中的配置

## 优势

1. **模块化设计**: 功能分离，便于维护和扩展
2. **代码复用**: 公共函数集中管理，避免重复代码
3. **易于调试**: 可以单独测试某个功能模块
4. **清晰注释**: 每个模块都有详细的中文注释
5. **保持兼容**: 完全保留原有的业务逻辑和功能

## 注意事项

- 确保 `modules` 目录与 `main.ahk` 在同一目录下
- Windows Terminal 路径需要根据实际安装位置调整
- 部分功能需要相应的工具程序存在（如 WindowCC.exe, kimiReverse 等）

## 维护建议

- 新增功能时，根据功能类型添加到相应模块
- 如果功能较复杂，可以创建新的专用模块
- 定期检查和更新模块间的依赖关系
