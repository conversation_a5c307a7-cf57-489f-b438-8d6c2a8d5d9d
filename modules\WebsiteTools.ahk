; ============================================================================
; 网站和工具启动模块 (WebsiteTools.ahk)
; 功能: 快捷键启动常用网站和工具
; 说明: 使用Alt和Ctrl+Alt组合键快速打开网站或启动工具
; ============================================================================

; Alt + 数字键快捷启动
!1::Run "http://127.0.0.1:11317"          ; Alt+1 打开本地assetTerm网站
!2::Run "https://sou.yancc.site/"         ; Alt+2 打开聚合搜索网站
!3::Run "http://192.168.1.28:7190/ops/remote"  ; Alt+3 打开堡垒机网站

; Ctrl + Alt + 字母键快捷启动
^!g::Run "https://github.com"             ; Ctrl+Alt+G 打开GitHub
^!o::Run "https://google.com"             ; Ctrl+Alt+O 打开Google
^!b::Run "https://baidu.com"              ; Ctrl+Alt+B 打开百度
^!d::Run "https://doubao.com"             ; Ctrl+Alt+D 打开豆包

; Alt + 大写字母工具启动
!G::Run "cmd /c d: && cd D:\\yanspace\\path\\googleAuth && googleAuth.exe"  ; Alt+G 启动谷歌验证工具
