; ============================================================================
; Windows Terminal 启动模块 (WindowsTerminal.ahk)
; 功能: 智能启动 Windows Terminal
; 说明: 在资源管理器中启动时带路径，在其他地方启动时使用默认路径
; ============================================================================

; 包含窗口管理器模块
#Include "WindowManager.ahk"

; Alt+R: 智能启动 Windows Terminal
!R::
{
    LaunchWindowsTerminal()
}

; 启动 Windows Terminal 的主函数
LaunchWindowsTerminal() {
    ; 检查是否在资源管理器中
    isInExplorer := WinActive("ahk_class CabinetWClass") || WinActive("ahk_class ExploreWClass")

    ; Windows Terminal 路径配置
    terminalPath := "C:\\tools\\WindowsTerminal\\WindowsTerminal.exe"

    ; 检查 Windows Terminal 是否存在
    if !FileExist(terminalPath) {
        ; 尝试使用系统默认的 wt 命令
        try {
            if (isInExplorer) {
                currentPath := GetExplorerPath()
                if (currentPath != "") {
                    Run('wt -d "' . currentPath . '"')
                } else {
                    Run("wt")
                }
            } else {
                Run("wt")
            }
            return
        } catch Error as e {
            MsgBox("Windows Terminal 未找到，请确保已安装 Windows Terminal", "错误", 0x10)
            return
        }
    }

    ; 构建命令行参数
    cmdArgs := ""
    if (isInExplorer) {
        currentPath := GetExplorerPath()
        if (currentPath != "") {
            cmdArgs := '-d "' . currentPath . '"'
        }
    }

    ; 运行 Windows Terminal
    try {
        if (cmdArgs != "") {
            Run(terminalPath . " " . cmdArgs)
        } else {
            Run(terminalPath)
        }
    } catch Error as e {
        MsgBox("启动 Windows Terminal 失败: " . e.message, "错误", 0x10)
        return
    }

    ; 等待窗口出现并激活
    if (WaitForTerminalWindow()) {
        WinActivate("ahk_exe WindowsTerminal.exe")
        Sleep(300) ; 给窗口一点时间来响应
    }
}
