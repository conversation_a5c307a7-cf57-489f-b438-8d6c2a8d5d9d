; ============================================================================
; 多显示器信息模块 (MonitorInfo.ahk)
; 功能: 显示当前显示器配置信息
; 说明: 使用Ctrl+Shift+I查看显示器信息，用于调试多显示器配置
; ============================================================================

; Ctrl+Shift+I: 显示当前显示器信息
^+i::
{
    info := "显示器数量: " . MonitorGetCount() . "`n`n"
    
    ; 遍历所有显示器并获取信息
    Loop MonitorGetCount() {
        MonitorGet(A_Index, &left, &top, &right, &bottom)
        MonitorGetWorkArea(A_Index, &wLeft, &wTop, &wRight, &wBottom)
        
        info .= "显示器 " . A_Index . ":`n"
        info .= "  完整区域: " . left . ", " . top . " 到 " . right . ", " . bottom . "`n"
        info .= "  工作区域: " . wLeft . ", " . wTop . " 到 " . wRight . ", " . wBottom . "`n"
        info .= "  分辨率: " . (right - left) . " x " . (bottom - top) . "`n`n"
    }
    
    MsgBox(info, "显示器信息")
}
