; ============================================================================
; 文本替换模块 (TextReplacement.ahk)
; 功能: 快速文本替换和代码片段插入
; 说明: 
;   :*:[原字符串]::[替代字符串]   ; 自动替换
;   ::[原字符串]::[替代字符串]   ; 按Enter后替换
; ============================================================================

; Go语言编译命令 - 普通版本
:*:gobf::{
    SendText("go build -ldflags `"-s -w`"")
}

; Go语言编译命令 - Windows GUI版本
:*:gobw::{
    SendText("go build -ldflags `"-H windowsgui -s -w`"")
}

; SQL查询语句模板
:*:selectf::{
    SendText("select t.*,t.rowid from")
}

; Sed命令 - 移除Windows换行符
:*:sedf::{
    SendText("sed -i -e 's/\r$//' *.sh")
}

; Go语言main函数模板
:*:mainf::{
    SendText("package main")
    Send("{Enter}")
    SendText("import `"fmt`"")
    Send("{Enter}")
    SendText("// go build -ldflags `"-s -w`"")
    Send("{Enter}")
    SendText("func main() {")
    Send("{Enter}")
    Send("{Tab}")
    SendText("fmt.Println(`"hello`")")
    Send("{Enter}")
    SendText("}")
}

; 百度网站快速打开
:*:baiduf::{
    Run "https://baidu.com"
}
