; ============================================================================
; 终端管理模块 (TerminalManager.ahk)
; 功能: 管理Windows Terminal的启动和窗口操作
; 说明: 提供终端启动、窗口移动、最大化等功能
; ============================================================================

; 包含窗口管理器模块
#Include "WindowManager.ahk"

; Alt+K: 启动Kimi逆向终端对话
!K::
{
    ; 获取显示器数量
    monitorCount := MonitorGetCount()
    ; 如果有多个显示器，则在第二个显示器上打开
    targetMonitor := (monitorCount >= 2) ? 2 : 1
    
    ; 构建命令行参数 - 使用 cmd 来运行 kimiReverse
    terminalPath := "C:\\tools\\WindowsTerminal\\WindowsTerminal.exe"
    cmdArgs := '-- cmd /k kimiReverse'
    
    ; 检查 Windows Terminal 是否存在
    if !FileExist(terminalPath) {
        MsgBox("Windows Terminal 路径不存在: " . terminalPath, "错误", 0x10)
        return
    }
    
    ; 运行 Windows Terminal
    try {
        Run(terminalPath . " " . cmdArgs)
    } catch Error as e {
        MsgBox("启动 Windows Terminal 失败: " . e.message, "错误", 0x10)
        return
    }
    
    ; 等待窗口出现并处理
    if (WaitForTerminalWindow()) {
        ; 激活窗口
        WinActivate("ahk_exe WindowsTerminal.exe")
        Sleep(500) ; 给窗口一点时间来响应
        
        ; 移动和最大化窗口
        try {
            MonitorGetWorkArea(targetMonitor, &workLeft, &workTop, &workRight, &workBottom)
            WinMove(workLeft + 100, workTop + 100,,, "ahk_exe WindowsTerminal.exe")
            WinMaximize("ahk_exe WindowsTerminal.exe")
        } catch Error as e {
            MsgBox("窗口操作失败: " . e.message, "错误", 0x10)
        }
    } else {
        MsgBox("Windows Terminal 窗口未找到，请检查程序是否正确启动", "错误", 0x10)
    }
}
